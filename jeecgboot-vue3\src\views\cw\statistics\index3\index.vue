<template>
  <!-- 价量分析页面 -->
  <div class="cw-jlfx-page">
    <a-card title="价量影响分析" :bordered="false">
      <template #extra>
        <a-radio-group v-model:value="chartType" button-style="solid" @change="updateBarChart">
          <a-radio-button value="price">价格影响</a-radio-button>
          <a-radio-button value="volume">产量影响</a-radio-button>
        </a-radio-group>
      </template>
      <!-- 查询条件 -->
      <a-space style="margin-bottom: 12px;" align="center">
        <a-radio-group v-model:value="dimension" @change="fetchBarData">
          <a-radio-button value="month">月</a-radio-button>
          <a-radio-button value="year">年</a-radio-button>
        </a-radio-group>
        <a-date-picker
          v-model:value="barDate"
          :picker="pickerType"
          :allowClear="false"
          @change="fetchBarData"
        />
      </a-space>

      <!-- 影响概览卡片 -->
      <div class="impact-summary">
        <!-- 价格影响列表 -->
        <div class="impact-item price">
          <div class="label">价格影响 (万元)</div>
          <div class="impact-list">
            <div class="impact-row" v-for="p in priceImpactList" :key="p.metal">
              <span class="metal">{{ p.metal }}</span>
              <span :class="p.value >= 0 ? 'pos' : 'neg'">{{ formatNumber(p.value) }}</span>
            </div>
          </div>
          <a-divider style="margin:6px 0" />
          <div class="total">合计：{{ formatNumber(totalPriceImpact) }}</div>
        </div>
        <!-- 产量影响列表 -->
        <div class="impact-item volume">
          <div class="label">产量影响 (万元)</div>
          <div class="impact-list">
            <div class="impact-row" v-for="v in volumeImpactList" :key="v.metal">
              <span class="metal">{{ v.metal }}</span>
              <span :class="v.value >= 0 ? 'pos' : 'neg'">{{ formatNumber(v.value) }}</span>
            </div>
          </div>
          <a-divider style="margin:6px 0" />
          <div class="total">合计：{{ formatNumber(totalVolumeImpact) }}</div>
        </div>
      </div>

      <div ref="barRef" class="bar-chart"></div>
    </a-card>
  </div>
</template>

<script lang="ts" setup name="cw-price-volume-statistics">
  // 价量分析脚本
  import { ref, onMounted, watch, computed } from 'vue';
  import dayjs from 'dayjs';
  import { message } from 'ant-design-vue';

  // 接口
  import { metalProfitBar } from '/@/api/cw/statistics';
  // 工具
  import { useECharts } from '/@/hooks/web/useECharts';
  import { formatNumber } from '/@/utils/showUtils';

  /** 查询条件 */
  const dimension = ref<'month' | 'year'>('month');
  const barDate = ref(dayjs());
  const pickerType = computed(() => dimension.value);

  /** 图表实例 */
  const barRef = ref<HTMLDivElement | null>(null);
  // @ts-ignore
  const { setOptions: setBarOptions } = useECharts(barRef as any);

  /** 图表类型切换: price -> 价格影响, volume -> 产量影响 */
  const chartType = ref<'price' | 'volume'>('price');

  /** 数据源 */
  const barList = ref<any[]>([]);

  /** 影响汇总 */
  const totalPriceImpact = computed(() => {
    return barList.value.reduce((sum, item) => sum + Number(item.priceImpact ?? 0), 0);
  });
  const totalVolumeImpact = computed(() => {
    return barList.value.reduce((sum, item) => sum + Number(item.volumeImpact ?? 0), 0);
  });

  /** 分金属影响列表 */
  const priceImpactList = computed(() => {
    return barList.value.map((item: any) => ({ metal: item.metal, value: Number(item.priceImpact ?? 0) }));
  });
  const volumeImpactList = computed(() => {
    return barList.value.map((item: any) => ({ metal: item.metal, value: Number(item.volumeImpact ?? 0) }));
  });

  /** 计划总利润 */
  const totalPlan = computed(() => {
    return barList.value.reduce((sum, item) => sum + Number(item.plan ?? 0), 0);
  });

  /** 监听变化自动刷新 */
  watch([dimension, barDate], () => fetchBarData());

  onMounted(() => {
    fetchBarData();
  });

  /** 获取图表数据 */
  async function fetchBarData() {
    try {
      const dateStr = barDate.value.format('YYYY-MM-DD');
      barList.value = await metalProfitBar({ date: dateStr, dimension: dimension.value });
      updateBarChart();
    } catch (e) {
      console.error(e);
      message.error('金属利润数据获取失败');
    }
  }

  /** 更新图表 */
  function updateBarChart() {
    if (!barList.value?.length) return;

    const isPriceChart = chartType.value === 'price';
    const options = generateWaterfallOptions(
      isPriceChart ? '价格影响' : '产量影响',
      '计划合计',
      totalPlan.value,
      isPriceChart ? priceImpactList.value : volumeImpactList.value,
      isPriceChart ? '价格影响后' : '产量影响后'
    );
    setBarOptions(options as any);
  }

  /**
   * 生成瀑布图配置
   * @param title - 图表标题
   * @param startLabel - 起始项标签
   * @param startValue - 起始值
   * @param impacts - 影响项列表 { metal: string, value: number }[]
   * @param endLabelPrefix - 结束项标签前缀
   */
  function generateWaterfallOptions(title, startLabel, startValue, impacts, endLabelPrefix) {
    const labels = [startLabel, ...impacts.map(item => item.metal), `${endLabelPrefix}合计`];
    
    // The data for the main series. The last item is an object for special styling.
    const data = [
      startValue,
      ...impacts.map(item => item.value),
      {
        value: impacts.reduce((sum, item) => sum + item.value, startValue),
        itemStyle: { color: '#91caff' } // Color for the final total bar
      }
    ];

    // The helper series for stacking to create the waterfall effect.
    const helpData =; // Initial value is always 0
    let sum = startValue;
    for (let i = 0; i < impacts.length; i++) {
        let val = impacts[i].value;
        if (val >= 0) {
            helpData.push(sum);
        } else {
            helpData.push(sum + val);
        }
        sum += val;
    }
    helpData.push(0); // Final total bar starts from 0

    return {
        title: { text: `${title}分析`, left: 'center' },
        grid: { left: 80, right: 40, bottom: 60, top: 30, containLabel: true },
        xAxis: { type: 'category', data: labels, axisLabel: { interval: 0, rotate: labels.length > 10 ? 30 : 0 } },
        yAxis: { type: 'value', axisLabel: { formatter: v => formatNumber(v) } },
        tooltip: {
            trigger: 'axis',
            axisPointer: { type: 'shadow' },
            formatter: (params) => {
                const param = params.find(p => p.seriesName === '变化');
                if (!param) return '';
                const value = typeof param.data === 'object' ? param.data.value : param.data;
                if (param.name.includes('合计')) {
                    return `${param.name}<br/>金额: ${formatNumber(value)}`;
                }
                return `${param.name}<br/>影响: ${formatNumber(value)}`;
            }
        },
        series: [
            {
                name: '辅助',
                type: 'bar',
                stack: 'waterfall',
                itemStyle: { borderColor: 'transparent', color: 'transparent' },
                emphasis: { itemStyle: { borderColor: 'transparent', color: 'transparent' } },
                data: helpData
            },
            {
                name: '变化',
                type: 'bar',
                stack: 'waterfall',
                label: { 
                    show: true, 
                    position: 'inside', 
                    formatter: p => {
                        const val = typeof p.data === 'object' ? p.data.value : p.data;
                        return formatNumber(val);
                    }
                },
                itemStyle: {
                    color: (p) => {
                        // Special styling for the final total bar
                        if (typeof p.data === 'object' && p.data.itemStyle) {
                            return p.data.itemStyle.color;
                        }
                        // Start total bar
                        if (p.dataIndex === 0) {
                             return '#91caff';
                        }
                        // Use red for positive impacts and green for negative
                        return p.value >= 0 ? '#ff7875' : '#95de64';
                    }
                },
                data: data
            }
        ]
    };
  }
</script>

<style scoped lang="less">
.cw-jlfx-page {
  .ant-card {
    margin-bottom: 16px;
    .bar-chart {
      width: 100%;
      height: calc(100vh - 200px); // 视口自适应高度
    }

    /* ==== Impact Cards Modern Style ==== */
    .impact-summary {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
      gap: 16px;
      margin: 12px 0;
    }

    .impact-item {
      background: #ffffff;
      border-radius: 10px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      padding: 16px 20px;
      display: flex;
      flex-direction: column;
    }

    .impact-item .label {
      font-size: 16px;
      font-weight: 600;
      color: #595959;
      margin-bottom: 8px;
    }

    .impact-list {
      flex: 1;
      border-top: 1px dashed #f0f0f0;
      padding-top: 8px;
      margin-top: 8px;
    }

    .impact-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 6px 4px; /* 增加左右间隙 */
      font-size: 14px;
    }

    .impact-row:not(:last-child) {
      border-bottom: 1px dashed #f0f0f0;
    }

    .impact-row .metal {
      display: inline-flex;
      align-items: center;
      gap: 4px;
    }

    .impact-row .metal::before {
      content: "";
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: #1890ff;
    }

    .impact-row .pos {
      color: #f5222d;
    }
    .impact-row .neg {
      color: #52c41a;
    }

    .total {
      margin-top: 10px;
      font-weight: 600;
      text-align: right;
      font-size: 18px; /* 更突出 */
    }
  }
}
</style>